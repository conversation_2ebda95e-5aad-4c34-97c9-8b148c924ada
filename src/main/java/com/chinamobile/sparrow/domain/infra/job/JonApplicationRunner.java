package com.chinamobile.sparrow.domain.infra.job;

import com.chinamobile.sparrow.domain.util.ClassUtil;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

public class JonApplicationRunner implements ApplicationRunner {

    protected final String packages;
    protected final Scheduler scheduler;
    protected final Logger logger;

    public JonApplicationRunner(@Value(value = "${job.packages}") String packages, Scheduler scheduler) {
        this.packages = packages;
        this.scheduler = scheduler;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String[] _packages = StringUtils.hasLength(packages)
                ? Arrays.stream(packages.split(","))
                .map(String::trim)
                .toArray(String[]::new)
                : new String[]{"com.chinamobile"};

        for (String i : _packages) {
            Set<Class<?>> _classes = ClassUtil.getClasses(i);
            _classes.forEach(j -> {
                try {
                    JobDetailAndTrigger _annotation = j.getAnnotation(JobDetailAndTrigger.class);
                    if (_annotation == null) {
                        return;
                    }

                    JobKey _jobKey = new JobKey(_annotation.jobName(), _annotation.jobGroup());
                    if (!scheduler.checkExists(_jobKey)) {
                        JobDetail _jobDetail = JobBuilder.newJob((Class<? extends Job>) j)
                                .withIdentity(_annotation.jobName(), _annotation.jobGroup())
                                .build();

                        TriggerKey _triggerKey = new TriggerKey(_annotation.triggerName(), _annotation.jobGroup());
                        CronTrigger _trigger = (CronTrigger) scheduler.getTrigger(_triggerKey);
                        if (_trigger == null) {
                            _trigger = TriggerBuilder.newTrigger()
                                    .withIdentity(_annotation.triggerName(), _annotation.triggerGroup())
                                    .withSchedule(CronScheduleBuilder.cronSchedule(_annotation.triggerCron()).withMisfireHandlingInstructionDoNothing())
                                    .startNow()
                                    .build();
                        }

                        scheduler.scheduleJob(_jobDetail, _trigger);
                    }

                    List<JobExecutionContext> _executions = scheduler.getCurrentlyExecutingJobs();

                    if (_annotation.triggerOnStart()) {
                        // 已运行
                        if (_executions.stream().anyMatch(k -> k.getJobDetail().getKey().equals(_jobKey))) {
                            return;
                        }

                        scheduler.triggerJob(_jobKey);
                    }
                } catch (Throwable e) {
                    logger.debug(String.format("读取JobDetailAndTrigger注解类，无法解析%s类", j.getName()), e);
                }
            });
        }
    }

}