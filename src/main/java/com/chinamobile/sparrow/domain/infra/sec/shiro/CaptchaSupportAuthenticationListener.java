package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;

public class CaptchaSupportAuthenticationListener extends DefaultAuthenticationListener {

    public CaptchaSupportAuthenticationListener(UserRepository userRepository) {
        super(userRepository);
    }

    @Override
    public void onFailure(AuthenticationToken authenticationToken, AuthenticationException e) {
        // 重置图形验证码
        SecurityUtils.getSubject().getSession(true).removeAttribute(LoginController.CAPTCHA_SESSION_ATTRIBUTE);

        super.onFailure(authenticationToken, e);
    }

}