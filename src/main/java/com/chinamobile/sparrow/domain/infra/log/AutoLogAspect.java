package com.chinamobile.sparrow.domain.infra.log;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.apache.commons.lang3.tuple.Pair;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.slf4j.event.Level;
import org.springframework.http.HttpMethod;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.validation.Errors;
import org.springframework.web.bind.support.SessionStatus;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MultipartRequest;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import org.springframework.web.util.UriComponentsBuilder;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Reader;
import java.io.Writer;
import java.security.Principal;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Aspect
public class AutoLogAspect {

    final int length;
    final HttpServletRequest request;
    final LoginUtil loginUtil;
    final Logger logger;

    public AutoLogAspect(int length, HttpServletRequest request, LoginUtil loginUtil) {
        this.length = length;
        this.request = request;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Around(value = "(@within(org.springframework.stereotype.Controller) || @within(org.springframework.web.bind.annotation.RestController) || @within(Log)|| @annotation(Log)) && !@within(NotLog) && !@annotation(NotLog)")
    public Object execute(ProceedingJoinPoint point) throws Throwable {
        String _startTime = DateUtil.toString(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");

        Integer _stack;

        boolean _isNewTransaction = isNewTransaction();
        if (_isNewTransaction) {
            // 记录事务
            String _id = UUID.randomUUID().toString().replace("-", "");
            MDC.put("transactionId", _id);

            // 记录堆栈
            _stack = 0;
            MDC.put("stack", _stack.toString());

            // 记录用户
            Pair<String, String> _user = getUser();
            MDC.put("realmType", _user == null ? null : _user.getLeft());
            MDC.put("actorId", _user == null ? null : _user.getRight());

            try {
                String _addr = request.getHeader("X-FORWARDED-FOR");
                if (!StringUtils.hasLength(_addr)) {
                    _addr = request.getRemoteAddr();
                }

                MDC.put("ip", _addr);
            } catch (Throwable e) {
                logger.debug("读取客户端IP失败", e);

                MDC.put("ip", null);
            }
        } else {
            // 更新堆栈
            _stack = Integer.parseInt(MDC.get("stack")) + 1;
            MDC.put("stack", _stack.toString());
        }

        String _reqStr = null, _resStr = null;
        Object _result = null;
        String _method = String.format("%s.%s", point.getSignature().getDeclaringTypeName(), point.getSignature().getName());
        try {
            try {
                if (point.getArgs().length > 0) {
                    // 排除自动注入对象
                    List<Object> _args = Arrays.stream(point.getArgs())
                            .filter(i -> !isResolvedArgument(i))
                            .collect(Collectors.toList());
                    _reqStr = ConverterUtil.toJson(_args);
                }

                if (StringUtils.hasLength(_reqStr) && _reqStr.length() > length) {
                    _reqStr = null;
                }
            } catch (Throwable e) {
                logger.debug("读取客户端请求数据失败", e);
            }

            _result = point.proceed();
        } catch (Throwable e) {
            String _endTime = DateUtil.toString(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");

            write(_method, String.valueOf(_stack), _reqStr, _resStr, Result.UNKNOWN, _startTime, _endTime, Level.ERROR, e);
            throw e;
        } finally {
            // 设置返回数据
            try {
                if (_result != null) {
                    _resStr = ConverterUtil.toJson(_result);
                }

                if (StringUtils.hasLength(_resStr) && _resStr.length() > length) {
                    _resStr = null;
                }
            } catch (Throwable e) {
                logger.debug("读取服务端响应数据失败", e);
            }

            String _code = _result instanceof Result ? ((Result<?>) _result).getCode() : Result.OK;
            String _endTime = DateUtil.toString(new Date(), "yyyy-MM-dd HH:mm:ss.SSS");

            write(_method, String.valueOf(_stack), _reqStr, _resStr, _code, _startTime, _endTime, Level.INFO, null);

            // 清理事务
            if (_isNewTransaction) {
                MDC.clear();
            }
        }

        return _result;
    }

    protected boolean isNewTransaction() {
        return !StringUtils.hasLength(MDC.get("transactionId"));
    }

    protected Pair<String, String> getUser() {
        try {
            String _auth = loginUtil.getRealmType(request);
            String _userId = String.valueOf(loginUtil.getUserId());

            return Pair.of(_auth, _userId);
        } catch (Throwable e) {
            logger.debug("读取用户失败", e);

            return null;
        }
    }

    protected void write(String message, String stack, String req, String res, String responseCode, String startTime, String endTime, Level level, Throwable e) {
        MDC.put("stack", stack);
        MDC.put("request", req);
        MDC.put("response", res);
        MDC.put("responseCode", StringUtils.hasLength(responseCode) ? responseCode.toUpperCase() : null);
        MDC.put("startTime", startTime);
        MDC.put("endTime", endTime);

        switch (level) {
            case INFO:
                logger.info(message);
                break;
            case ERROR:
                logger.error(message, e);
                break;
        }
    }

    boolean isResolvedArgument(Object param) {
        Class<?> _paramType = param.getClass();

        return isServletRequestMethodArgument(_paramType) || isServletResponseMethodArgument(_paramType) || isRedirectAttributesMethodArgument(_paramType) || isModelMethodArgument(_paramType) || isErrorsMethodArgument(_paramType) || isSessionStatusMethodArgument(_paramType) || isUriComponentsBuilderMethodArgument(_paramType);
    }

    boolean isServletRequestMethodArgument(Class<?> paramType) {
        return WebRequest.class.isAssignableFrom(paramType) || ServletRequest.class.isAssignableFrom(paramType) || MultipartRequest.class.isAssignableFrom(paramType) || HttpSession.class.isAssignableFrom(paramType) || Principal.class.isAssignableFrom(paramType) || InputStream.class.isAssignableFrom(paramType) || Reader.class.isAssignableFrom(paramType) || HttpMethod.class == paramType || Locale.class == paramType || TimeZone.class == paramType || ZoneId.class == paramType;
    }

    boolean isServletResponseMethodArgument(Class<?> paramType) {
        return ServletResponse.class.isAssignableFrom(paramType) || OutputStream.class.isAssignableFrom(paramType) || Writer.class.isAssignableFrom(paramType);
    }

    boolean isRedirectAttributesMethodArgument(Class<?> paramType) {
        return RedirectAttributes.class.isAssignableFrom(paramType);
    }

    boolean isModelMethodArgument(Class<?> paramType) {
        return Model.class.isAssignableFrom(paramType);
    }

    boolean isErrorsMethodArgument(Class<?> paramType) {
        return Errors.class.isAssignableFrom(paramType);
    }

    boolean isSessionStatusMethodArgument(Class<?> paramType) {
        return SessionStatus.class == paramType;
    }

    boolean isUriComponentsBuilderMethodArgument(Class<?> paramType) {
        return UriComponentsBuilder.class == paramType || ServletUriComponentsBuilder.class == paramType;
    }

}