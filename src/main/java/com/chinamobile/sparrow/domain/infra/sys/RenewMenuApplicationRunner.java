package com.chinamobile.sparrow.domain.infra.sys;

import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

public class RenewMenuApplicationRunner implements ApplicationRunner {

    protected final PageRepository pageRepository;
    protected final Logger logger;

    public RenewMenuApplicationRunner(PageRepository pageRepository) {
        this.pageRepository = pageRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void run(ApplicationArguments args) {
        try {
            pageRepository.renew();
        } catch (Throwable e) {
            logger.error("更新菜单失败，如果需要更新菜单建议重新启动应用", e);
        }
    }

}