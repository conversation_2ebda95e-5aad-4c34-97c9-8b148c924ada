package com.chinamobile.sparrow.domain.infra.sec.shiro;

import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

public class RenewPermissionApplicationRunner implements ApplicationRunner {

    protected final PermissionRepository permissionRepository;
    protected final Logger logger;

    public RenewPermissionApplicationRunner(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public void run(ApplicationArguments args) {
        try {
            permissionRepository.renew(null);
        } catch (Throwable e) {
            logger.error("更新权限失败，如果需要更新权限建议重新启动应用", e);
        }
    }

}