package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/dept")
public class DepartmentController {

    protected final DepartmentRepository departmentRepository;
    protected final LoginUtil loginUtil;

    public DepartmentController(DepartmentRepository departmentRepository, LoginUtil loginUtil) {
        this.departmentRepository = departmentRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "sys:dept:save")
    public Result<String> save(@RequestBody @Validated Department record) throws InstantiationException, IllegalAccessException {
        return departmentRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/move")
    @ResponseBody
    @RequiresPermissions(value = "sys:dept:move")
    public Result<Void> save(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _superiorId = data.get("superiorId").isJsonNull() ? null : data.get("superiorId").getAsString();

        return departmentRepository.move(_id, _superiorId, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "sys:dept:remove")
    public Result<Void> remove(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _id = data.get("id").getAsString();

        return departmentRepository.disable(_id, loginUtil.getUserId());
    }

    @PostMapping(value = "/organization")
    @ResponseBody
    public Result<DepartmentRepository.Organization<?>> organization(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        return departmentRepository.getAsOrganization(_id);
    }

    @PostMapping(value = "/user/search")
    @ResponseBody
    public Result<PaginatedRecords<User>> members(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _id = Optional.ofNullable(data.get("id"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _account = Optional.ofNullable(data.get("account"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _username = Optional.ofNullable(data.get("username"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _isEnabled = Optional.ofNullable(data.get("isEnabled"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);

        Result<PaginatedRecords<User>> _page = new Result<>();
        _page.data = departmentRepository.members(_count, _index, _sorters, _id, _account, _username, _mp, _isEnabled);
        return _page;
    }

}