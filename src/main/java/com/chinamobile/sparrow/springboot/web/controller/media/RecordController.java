package com.chinamobile.sparrow.springboot.web.controller.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

@Controller
@RequestMapping(value = "media")
public class RecordController {

    protected final AbstractMediaRepository mediaRepository;
    protected final LoginUtil loginUtil;
    protected final Logger logger;

    public RecordController(AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        this.mediaRepository = mediaRepository;
        this.loginUtil = loginUtil;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @PostMapping(value = "/search")
    @ResponseBody
    @RequiresPermissions(value = "media:record:search")
    public Result<PaginatedRecords<Media>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _path = Optional.ofNullable(data.get("path"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _temporary = Optional.ofNullable(data.get("temporary"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);

        Result<PaginatedRecords<Media>> _page = new Result<>();
        _page.data = mediaRepository.search(_count, _index, _sorters, _path, _name, _temporary);
        mediaRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/sub-paths")
    @ResponseBody
    public Result<List<ImmutablePair<String, String>>> subPaths(@RequestBody JsonObject data) {
        String _path = data.get("path").getAsString();

        Result<List<ImmutablePair<String, String>>> _paths = new Result<>();
        _paths.data = mediaRepository.getSubPaths(_path);
        return _paths;
    }

    @PostMapping(value = "/stage/base64")
    @ResponseBody
    public Result<List<AddMediaResult>> stage(@RequestBody JsonObject data) {
        return addSerializedFiles((triple) -> mediaRepository.stage(triple.getLeft(), triple.getMiddle(), triple.getRight(), loginUtil.getUserId()), data);
    }

    @PostMapping(value = "/stage/form")
    @ResponseBody
    public Result<List<AddMediaResult>> stage(@RequestParam(value = "bucket", required = false) String bucket, @RequestParam(value = "files") MultipartFile[] files) {
        return addFiles((pair) -> mediaRepository.stage(pair.getLeft(), pair.getRight(), loginUtil.getUserId()), bucket, files);
    }

    @PostMapping(value = "/add/base64")
    @ResponseBody
    public Result<List<AddMediaResult>> add(@RequestBody JsonObject data) {
        return addSerializedFiles((triple) -> mediaRepository.add(triple.getLeft(), triple.getMiddle(), triple.getRight(), true, loginUtil.getUserId()), data);
    }

    @PostMapping(value = "/add/form")
    @ResponseBody
    public Result<List<AddMediaResult>> add(@RequestParam(value = "bucket", required = false) String bucket, @RequestParam(value = "files") MultipartFile[] files) {
        return addFiles((pair) -> mediaRepository.add(pair.getLeft(), pair.getRight(), true, loginUtil.getUserId()), bucket, files);
    }

    @PostMapping(value = "/remove/force")
    @ResponseBody
    @RequiresPermissions(value = "media:record:remove")
    public Result<Void> removeWithForce(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return mediaRepository.remove(_id, true, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return mediaRepository.remove(_id, false, loginUtil.getUserId());
    }

    @RequestMapping(value = "/download")
    public void download(HttpServletResponse response, @RequestParam(value = "id") String id) throws Exception {
        writeToResponse(response, id, true);
    }

    public void writeToResponse(HttpServletResponse response, String id, boolean asAttachment) throws Exception {
        Result<Media> _record = mediaRepository.get(id, loginUtil.getUserId());
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        if (asAttachment) {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(_record.data.getName(), StandardCharsets.UTF_8.name()));
        }

        Result<InputStream> _input = mediaRepository.getInputStream(_record.data);
        if (_input.isOK()) {
            FileCopyUtils.copy(_input.data, response.getOutputStream());
        } else {
            throw new Exception(_input.message);
        }
    }

    protected Result<List<AddMediaResult>> addSerializedFiles(Function<Triple<String, String, String>, Result<Media>> func, JsonObject data) {
        Result<List<AddMediaResult>> _res = new Result<>();
        _res.data = new ArrayList<>();

        String _bucket = Optional.ofNullable(data.get("bucket"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        JsonArray _records = data.get("records").getAsJsonArray();
        for (JsonElement i : _records) {
            JsonObject _json = i.getAsJsonObject();
            String _name = _json.get("name").getAsString();
            String _base64 = _json.get("base64").getAsString();
            if (_base64.startsWith("data:")) { // 删除编码标识
                _base64 = _base64.split(",", 2)[1];
            }

            Result<Media> _record = func.apply(Triple.of(_bucket, _name, _base64));

            AddMediaResult _result = new AddMediaResult();
            _result.setSuccess(_record.isOK());
            if (_result.getSuccess()) {
                _result.setId(_record.data.getId());
                _result.setName(_record.data.getName());
                _result.setContent(_record.data.getContent());
            } else {
                _result.setMessage(_record.message);
            }

            _res.data.add(_result);
        }

        return _res;
    }

    protected Result<List<AddMediaResult>> addFiles(Function<Pair<String, File>, Result<Media>> func, String bucket, MultipartFile[] files) {
        Result<List<AddMediaResult>> _results = new Result<>();
        _results.data = new ArrayList<>();

        for (MultipartFile i : files) {
            File _file = null;

            try {
                _file = new File(FileUtils.getTempDirectory() + File.separator + i.getOriginalFilename());
                FileUtils.copyInputStreamToFile(i.getInputStream(), _file);

                Result<Media> _record = func.apply(Pair.of(bucket, _file));

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(_record.isOK());
                if (_result.getSuccess()) {
                    _result.setId(_record.data.getId());
                    _result.setName(_record.data.getName());
                    _result.setContent(_record.data.getContent());
                } else {
                    _result.setMessage(_record.message);
                }

                _results.data.add(_result);
            } catch (Throwable e) {
                logger.error(e.getMessage(), e);

                AddMediaResult _result = new AddMediaResult();
                _result.setSuccess(false);
                _result.message = e.getMessage();

                _results.data.add(_result);
            } finally {
                if (_file != null) {
                    _file.delete();
                }
            }
        }

        return _results;
    }

    public static class AddMediaResult {

        boolean success;
        String message;
        String id;
        String name;
        String content;

        public boolean getSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

    }

}