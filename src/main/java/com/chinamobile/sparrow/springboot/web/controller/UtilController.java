package com.chinamobile.sparrow.springboot.web.controller;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.util.ClassUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.persistence.Column;
import javax.persistence.Id;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(value = "util")
public class UtilController {

    protected final String cmPassportAppId;
    protected final String aMapJsKey;
    protected final String aMapJsCode;
    protected final String qqMapJsKey;

    public UtilController(String cmPassportAppId, String aMapJsKey, String aMapJsCode, String qqMapJsKey) {
        this.cmPassportAppId = cmPassportAppId;
        this.aMapJsKey = aMapJsKey;
        this.aMapJsCode = aMapJsCode;
        this.qqMapJsKey = qqMapJsKey;
    }

    @PostMapping(value = "/validation/jquery")
    @NotLog
    public Result<JsonObject> validation(@RequestBody JsonObject data) throws ClassNotFoundException {
        String _str = data.get("class").getAsString();
        Class<?> _tClass = Class.forName(_str);

        Result<JsonObject> _rules = new Result<>();
        _rules.data = new JsonObject();

        List<Field> _fields = ClassUtil.getFields(_tClass);
        for (Field i : _fields) {
            JsonObject _json = new JsonObject();

            JsonObject _rule = new JsonObject();

            boolean _required = false;

            // 主键必填
            Id _id = i.getAnnotation(Id.class);
            if (_id != null) {
                _required = true;
                _rule.addProperty("required", true);
            }

            Column _column = i.getAnnotation(Column.class);
            if (_column != null) {
                // 必填
                if (_id == null && !_column.nullable()) {
                    _required = true;
                    _rule.addProperty("required", true);
                }

                // 长度
                if (!StringUtils.hasLength(_column.columnDefinition())) {
                    int _length = _column.length();
                    _rule.addProperty("maxlength", _length);
                }
            }

            // 设置类型
            if (i.getType() == Short.class || i.getType() == Integer.class || i.getType() == Long.class) {
                _rule.addProperty("digits", true);
            } else if (i.getType() == Boolean.class) {
                JsonArray _options = new JsonArray();

                JsonObject _option = new JsonObject();
                _option.addProperty("key", true);
                _option.addProperty("value", "是");
                _options.add(_option);

                _option = new JsonObject();
                _option.addProperty("key", false);
                _option.addProperty("value", "否");
                _options.add(_option);

                if (!_required) {
                    _option = new JsonObject();
                    _option.addProperty("key", (Boolean) null);
                    _option.addProperty("value", "无");
                }

                _json.add("options", _options);
            } else if (i.getType() == Float.class || i.getType() == Double.class || i.getType() == BigDecimal.class) {
                _rule.addProperty("number", true);
            } else if (i.getType() == Date.class) {
                _rule.addProperty("date", true);
            } else if (i.getType().isEnum()) {
                JsonArray _options = new JsonArray();

                Enum<?>[] _types = (Enum<?>[]) i.getType().getEnumConstants();
                Arrays.stream(_types).forEach(j -> {
                    JsonObject _option = new JsonObject();
                    _option.addProperty("key", j.name());
                    _option.addProperty("value", j.name());
                    _options.add(_option);
                });

                if (!_required) {
                    JsonObject _option = new JsonObject();
                    _option.addProperty("key", (String) null);
                    _option.addProperty("value", "无");
                    _options.add(_option);
                }

                _json.add("options", _options);
            }

            _json.add("rule", _rule);

            _rules.data.add(i.getName(), _json);
        }

        return _rules;
    }

    // 移动认证appId
    @PostMapping(value = "/cmpassport/app-id")
    @NotLog
    public Result<String> cmPassportAppId() {
        Result<String> _str = new Result<>();
        _str.data = cmPassportAppId;
        return _str;
    }

    // 高德地图js key
    @PostMapping(value = "/amap/js-key")
    @NotLog
    public Result<JsonObject> aMapJsKey() {
        Result<JsonObject> _key = new Result<>();
        _key.data = new JsonObject();
        _key.data.addProperty("key", aMapJsKey);
        _key.data.addProperty("code", aMapJsCode);
        return _key;
    }

    // 腾讯地图js key
    @PostMapping(value = "/qqmap/js-key")
    @NotLog
    public Result<String> qqMapJsKey() {
        Result<String> _key = new Result<>();
        _key.data = qqMapJsKey;
        return _key;
    }

}