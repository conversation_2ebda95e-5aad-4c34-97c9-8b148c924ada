package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.UsernamePasswordAuthorizingRealm;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.CMPassportToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.SMSToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.WxCpToken;
import com.chinamobile.sparrow.domain.infra.sec.shiro.token.YzyToken;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import com.wf.captcha.SpecCaptcha;
import com.wf.captcha.base.Captcha;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Controller
@RequestMapping(value = "sec")
public class LoginController {

    public final static String CAPTCHA_SESSION_ATTRIBUTE = "CAPTCHA";

    protected final String rsaPublicKey;
    protected final String rsaPrivateKey;

    protected final VerificationCodeRepository verificationCodeRepository;

    protected final Facade cmPassportFacade;
    protected final AccessFacade wxCpAccessFacade;
    protected final AccessFacade yzyAccessFacade;
    protected final LoginUtil loginUtil;

    public LoginController(String rsaPublicKey, String rsaPrivateKey, VerificationCodeRepository verificationCodeRepository, Facade cmPassportFacade, AccessFacade wxCpAccessFacade, AccessFacade yzyAccessFacade, LoginUtil loginUtil) {
        this.rsaPublicKey = rsaPublicKey;
        this.rsaPrivateKey = rsaPrivateKey;
        this.verificationCodeRepository = verificationCodeRepository;
        this.cmPassportFacade = cmPassportFacade;
        this.wxCpAccessFacade = wxCpAccessFacade;
        this.yzyAccessFacade = yzyAccessFacade;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/login/crypto/rsa/public-key")
    @ResponseBody
    public Result<String> rsaPublicKey() {
        Result<String> _key = new Result<>();
        _key.data = this.rsaPublicKey;
        return _key;
    }

    /**
     * 生成图形验证码
     *
     * @param data
     * @return
     * @throws IOException
     * @throws FontFormatException
     */
    @PostMapping(value = "/login/captcha")
    @ResponseBody
    @NotLog
    public Result<JsonObject> captcha(@RequestBody JsonObject data) throws IOException, FontFormatException {
        int _width = data.get("width").getAsInt();
        int _height = data.get("height").getAsInt();
        int _length = data.get("length").getAsInt();

        // 设置宽、高、位数
        SpecCaptcha _captcha = new SpecCaptcha(_width, _height, _length);

        // 设置字体
        /*String[] _fonts = new String[]{"actionj.ttf", "epilog.ttf", "fresnel.ttf", "headache.ttf", "lexo.ttf", "prefix.ttf", "progbot.ttf", "ransom.ttf", "robot.ttf", "scandal.ttf"};*/
        String[] _fonts = new String[]{"epilog.ttf"};
        _captcha.setFont(Font.createFont(0, Objects.requireNonNull(this.getClass().getResourceAsStream("/" + _fonts[new Random().nextInt(_fonts.length)])))
                .deriveFont(Font.BOLD, 20));

        // 设置类型
        _captcha.setCharType(Captcha.TYPE_DEFAULT);

        SecurityUtils.getSubject().getSession(true).setAttribute(CAPTCHA_SESSION_ATTRIBUTE, _captcha.text());

        Result<JsonObject> _json = new Result<>();
        _json.data = new JsonObject();
        _json.data.addProperty("token", UUID.randomUUID().toString());
        _json.data.addProperty("image", _captcha.toBase64());
        return _json;
    }

    /**
     * 生成短信验证码
     *
     * @param clientId
     * @param data
     * @return
     */
    @PostMapping(value = "/login/sms-code")
    @ResponseBody
    public Result<Long> smsCode(@Value(value = "${sec.oauth2.client-id}") String clientId, @RequestBody JsonObject data) {
        String _mp = data.get("mp").getAsString();

        return verificationCodeRepository.add(clientId, _mp, null);
    }

    /**
     * 帐号密码登录
     *
     * @param request
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/login/password")
    @ResponseBody
    public Result<String> loginByPassword(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        return loginUtil.login(request, data, UsernamePasswordAuthorizingRealm.REALM_TYPE, true, false);
    }

    /**
     * 短信验证码登录
     *
     * @param request
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/login/sms")
    @ResponseBody
    public Result<String> loginBySMS(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        String _mp = data.get("mp").getAsString();
        String _smsCode = data.get("smsCode").getAsString();
        boolean _rememberMe = Optional.ofNullable(data.get("rememberMe"))
                .map(i -> !i.isJsonNull() && i.getAsBoolean())
                .orElse(false);
        String _redirect = Optional.ofNullable(data.get("redirect"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        // 登录
        return loginUtil.login(request, new SMSToken(_mp, _smsCode, _rememberMe, request.getRemoteHost()), _redirect);
    }

    /**
     * 移动认证签名
     *
     * @param data
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/login/cmpassport/sign")
    @ResponseBody
    public Result<String> sign(@RequestBody JsonObject data) throws Exception {
        String _str = data.get("str").getAsString();
        _str = cmPassportFacade.sign(_str);

        Result<String> _sign = new Result<>();
        _sign.data = _str;
        return _sign;
    }

    @PostMapping(value = "/login/cmpassport")
    @ResponseBody
    public Result<String> loginByCMPassport(HttpServletRequest request, @RequestBody JsonObject data) throws Exception {
        String _token = data.get("token").getAsString();
        String _userInformation = data.get("userInformation").getAsString();
        String _redirect = Optional.ofNullable(data.get("redirect"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        return loginUtil.login(request, new CMPassportToken(_token, _userInformation, false, request.getRemoteHost()), _redirect);
    }

    @PostMapping(value = "/login/wx")
    @ResponseBody
    public Result<String> loginByWX(@Value(value = "${wx.cp.redirect}") String redirect, @RequestBody JsonObject data) throws UnsupportedEncodingException {
        String _redirect = Optional.of(data.get("redirect"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        // 保存原地址
        String _str = String.format("%s/sec/login/wx/redirect", redirect);
        if (StringUtils.hasLength(_redirect)) {
            _str += "?url=" + URLEncoder.encode(_redirect, StandardCharsets.UTF_8.name());
        }

        // 设置重定向地址
        Result<String> _url = new Result<>();
        _url.data = wxCpAccessFacade.getAuthorizationUrl(_str);
        return _url;
    }

    @GetMapping(value = "/login/wx/redirect")
    public String redirectToRefererByWX(HttpServletRequest request, @RequestParam(value = "code") String code, @RequestParam(value = "url", required = false) String url) throws Exception {
        Result<String> _url = loginUtil.login(request, new WxCpToken(code, false, request.getRemoteHost()), StringUtils.hasLength(url) ? URLDecoder.decode(url, StandardCharsets.UTF_8.name()) : null);
        if (!_url.isOK()) {
            throw new Exception(_url.message);
        }

        return "redirect:" + _url.data;
    }

    @PostMapping(value = "/login/yzy")
    @ResponseBody
    public Result<String> loginByYZY(@Value(value = "${wx.yzy.redirect}") String redirect, @RequestBody JsonObject data) throws UnsupportedEncodingException {
        String _redirect = Optional.of(data.get("redirect"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        // 保存原地址
        String _str = String.format("%s/sec/login/yzy/redirect", redirect);
        if (StringUtils.hasLength(_redirect)) {
            _str += "?url=" + URLEncoder.encode(_redirect, StandardCharsets.UTF_8.name());
        }

        // 设置重定向地址
        Result<String> _url = new Result<>();
        _url.data = yzyAccessFacade.getAuthorizationUrl(_str);
        return _url;
    }

    @GetMapping(value = "/login/yzy/redirect")
    public String redirectToRefererByYZY(HttpServletRequest request, @RequestParam(value = "code") String code, @RequestParam(value = "url", required = false) String url) throws Exception {
        Result<String> _url = loginUtil.login(request, new YzyToken(code, false, request.getRemoteHost()), StringUtils.hasLength(url) ? URLDecoder.decode(url, StandardCharsets.UTF_8.name()) : null);
        if (!_url.isOK()) {
            throw new Exception(_url.message);
        }

        return "redirect:" + _url.data;
    }

    /**
     * 保活
     *
     * @return
     */
    @PostMapping(value = "/login/keep-alive")
    @ResponseBody
    @NotLog
    public Result<Date> keepAlive() {
        Date _lastAccessTime = SecurityUtils.getSubject().getSession(true).getLastAccessTime();
        long _timeout = SecurityUtils.getSubject().getSession().getTimeout();

        Result<Date> _exp = new Result<>();
        _exp.data = DateUtil.addSeconds(_lastAccessTime, (int) (_timeout / 1000));
        return _exp;
    }

    /**
     * 注销登录
     *
     * @return
     */
    @PostMapping(value = "/login/cancel")
    @ResponseBody
    public Result<Void> logout(HttpServletRequest request, HttpServletResponse response) {
        SecurityUtils.getSubject().logout();

        return new Result<>();
    }

}