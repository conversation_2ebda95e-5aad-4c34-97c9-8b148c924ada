package com.chinamobile.sparrow.springboot.web.autoconfigure;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.log.AutoLogAspect;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.*;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.chinamobile.sparrow.domain.util.I18NUtil;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.WxCpConfigStorage;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import okhttp3.ConnectionPool;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.http.HttpServletRequest;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class BeanAutoConfiguration {

    @Bean
    @ConditionalOnProperty(value = "aspect.log.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public AutoLogAspect autoLogAspect(@Value(value = "${aspect.log.max-length}") int length, HttpServletRequest request, LoginUtil loginUtil) {
        return new AutoLogAspect(length, request, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ConnectionPool defaultConnectionPool(@Value(value = "${okhttp.connection-pool.default.max-idle}") int maxIdle, @Value(value = "${okhttp.connection-pool.default.keep-alive}") int keepAlive) {
        return new ConnectionPool(maxIdle, keepAlive, TimeUnit.SECONDS);
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultResultParser resultParser() {
        return new DefaultResultParser();
    }

    @Bean
    @ConditionalOnProperty(value = "shiro.enabled", matchIfMissing = true)
    @ConditionalOnMissingBean
    public LoginUtil loginUtil(@Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey, @Value(value = "${sec.username}") String username, UserRepository userRepository, SessionDAO sessionDAO) {
        return new LoginUtil(rsaPrivateKey, username, userRepository, sessionDAO);
    }

    @Bean
    @ConditionalOnMissingBean
    public HttpUtil httpUtil(ConnectionPool connectionPool, @Autowired(required = false) @Qualifier(value = "defaultProxy") Proxy proxy) {
        return new HttpUtil(connectionPool, proxy);
    }

    @Bean
    @ConditionalOnProperty(value = "proxy")
    @ConditionalOnMissingBean(name = "defaultProxy")
    public Proxy defaultProxy(@Value(value = "${proxy.host}") String host, @Value(value = "${proxy.port}") int port) {
        return new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
    }

    @Bean
    @ConditionalOnMissingBean
    public ThreadPoolExecutor defaultThreadPool(@Value(value = "${thread-pool.default.core}") int core, @Value(value = "${thread-pool.default.max}") int max) {
        return new ThreadPoolExecutor(core, max, 1, TimeUnit.SECONDS, new ArrayBlockingQueue<>(core), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Bean
    @ConditionalOnProperty(value = "cmpassport.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public Facade cmPassportFacade(@Value(value = "${cmpassport.base-url}") String baseURL, @Value(value = "${cmpassport.app-id}") String appId, @Value(value = "${cmpassport.app-key}") String appKey, @Value(value = "${cmpassport.rsa.private-key}") String privateKey, HttpUtil httpUtil) {
        return new Facade(baseURL, appId, appKey, privateKey, httpUtil);
    }

    @Bean
    @ConditionalOnProperty(value = "s3.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public S3Adapter s3Adapter(@Value(value = "${s3.endpoint}") String endpoint, @Value(value = "${s3.access-key}") String accessKey, @Value(value = "${s3.secret-key}") String secretKey, @Value(value = "${file.part.size}") int partSize) {
        return new S3Adapter(endpoint, accessKey, secretKey, partSize);
    }

    @Bean
    @ConditionalOnMissingBean(value = IFileService.class)
    public FileSystemService fileSystemService(@Value(value = "${file.part.size}") int partSize) {
        return new FileSystemService(partSize);
    }

    @Bean
    @ConditionalOnMissingBean
    public I18NUtil i18NUtil() {
        return new I18NUtil();
    }

    @Bean
    @ConditionalOnProperty(value = "mas.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public MASFacade masFacade(@Value(value = "${mas.sms.base-url}") String baseURL, @Value(value = "${mas.sms.ec-name}") String ecName, @Value(value = "${mas.sms.ap-id}") String apId, @Value(value = "${mas.sms.secret-key}") String secretKey, HttpUtil httpUtil) {
        return new MASFacade(baseURL, ecName, apId, secretKey, httpUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public QuartzFacade quartzFacade(Scheduler scheduler) {
        return new QuartzFacade(scheduler);
    }

    @Bean
    @ConditionalOnProperty(value = "qxt.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public QXTFacade qxtFacade(@Value(value = "${qxt.base-url}") String baseURL, @Value(value = "${qxt.eid}") String eid, @Value(value = "${qxt.userId}") String userId, @Value(value = "${qxt.password}") String password, @Value(value = "${qxt.encrypt-key}") String encryptKey, @Value(value = "${qxt.port.default}") String port, HttpUtil httpUtil) {
        return new QXTFacade(baseURL, eid, userId, password, encryptKey, port, httpUtil);
    }

    @Bean
    @ConditionalOnProperty(value = "wx.cp.enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "wxCpConfigStorage")
    public WxCpConfigStorage wxCpConfigStorage(@Value(value = "${wx.cp.corp-id}") String corpId, @Value(value = "${wx.cp.corp-secret}") String corpSecret) {
        WxCpDefaultConfigImpl _config = new WxCpDefaultConfigImpl();
        _config.setCorpId(corpId);
        _config.setCorpSecret(corpSecret);

        return _config;
    }

    @Bean
    @ConditionalOnBean(name = "wxCpConfigStorage")
    @ConditionalOnMissingBean(name = "wxCpService")
    public WxCpService wxCpService(@Qualifier(value = "wxCpConfigStorage") WxCpConfigStorage wxCpConfigStorage) {
        WxCpService _service = new WxCpServiceImpl();
        _service.setWxCpConfigStorage(wxCpConfigStorage);
        return _service;
    }

    @Bean
    @ConditionalOnBean(name = "wxCpService")
    public AccessFacade wxCpAccessFacade(WxCpService wxCpService) {
        return new AccessFacade(wxCpService);
    }

    @Bean
    @ConditionalOnProperty(value = "wx.ma.enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "wxMaConfig")
    public WxMaConfig wxMaConfig(@Value("${wx.ma.app-id}") String appId, @Value("${wx.ma.secret}") String secret, @Value("${wx.ma.msg-data-format}") String msgDataFormat) {
        WxMaDefaultConfigImpl _config = new WxMaDefaultConfigImpl();
        _config.setAppid(appId);
        _config.setSecret(secret);
        _config.setMsgDataFormat(msgDataFormat);

        return _config;
    }

    @Bean
    @ConditionalOnBean(name = "wxMaConfig")
    @ConditionalOnMissingBean(name = "wxMaService")
    public WxMaService wxMaService(@Qualifier(value = "wxMaConfig") WxMaConfig wxMaConfig) {
        WxMaServiceImpl _service = new WxMaServiceImpl();
        _service.setWxMaConfig(wxMaConfig);

        return _service;
    }

    @Bean
    @ConditionalOnBean(name = "wxMaService")
    public com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade(WxMaService wxMaService) {
        return new com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade(wxMaService);
    }

    @Bean
    @ConditionalOnProperty(value = "wx.yzy.enabled", havingValue = "true")
    @ConditionalOnMissingBean(name = "yzyConfigStorage")
    public WxCpConfigStorage yzyConfigStorage(@Value(value = "${wx.yzy.base-url}") String baseURL, @Value(value = "${wx.yzy.corp-id}") String corpId, @Value(value = "${wx.yzy.corp-secret}") String corpSecret) {
        WxCpDefaultConfigImpl _config = new WxCpDefaultConfigImpl();
        _config.setBaseApiUrl(baseURL);
        _config.setCorpId(corpId);
        _config.setCorpSecret(corpSecret);

        return _config;
    }

    @Bean
    @ConditionalOnBean(name = "yzyConfigStorage")
    @ConditionalOnMissingBean(name = "yzyService")
    public WxCpService yzyService(@Qualifier(value = "yzyConfigStorage") WxCpConfigStorage wxCpConfigStorage) {
        WxCpService _service = new WxCpServiceImpl();
        _service.setWxCpConfigStorage(wxCpConfigStorage);
        return _service;
    }

    @Bean
    @ConditionalOnBean(name = "yzyService")
    public AccessFacade yzyAccessFacade(WxCpService yzyService) {
        return new AccessFacade(yzyService);
    }

}