package com.chinamobile.sparrow.springboot.web.autoconfigure;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisAutoConfiguration {

    @Bean
    public RedisSerializer<Object> jackson2JsonRedisSerializer() {
        ObjectMapper _mapper = new ObjectMapper();
        _mapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        _mapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);

        Jackson2JsonRedisSerializer<Object> _serializer = new Jackson2JsonRedisSerializer<>(Object.class);
        _serializer.setObjectMapper(_mapper);
        return _serializer;
    }

    @Bean
    @ConditionalOnMissingBean
    public RedisTemplate<String, Object> mainJsonRedisTemplate(RedisConnectionFactory redisConnectionFactory, RedisSerializer<Object> jackson2JsonRedisSerializer) {
        RedisTemplate<String, Object> _redisTemplate = new RedisTemplate<>();
        _redisTemplate.setConnectionFactory(redisConnectionFactory);

        _redisTemplate.setKeySerializer(new StringRedisSerializer());
        _redisTemplate.setValueSerializer(jackson2JsonRedisSerializer);
        _redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        _redisTemplate.setHashValueSerializer(jackson2JsonRedisSerializer);
        _redisTemplate.afterPropertiesSet();

        return _redisTemplate;
    }

}