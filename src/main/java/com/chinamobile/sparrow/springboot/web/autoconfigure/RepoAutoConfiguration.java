package com.chinamobile.sparrow.springboot.web.autoconfigure;

import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.media.FSSRepository;
import com.chinamobile.sparrow.domain.repository.media.OSSRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.*;
import com.chinamobile.sparrow.domain.repository.sys.log.LogRepository;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import com.chinamobile.sparrow.domain.service.FileSystemService;
import com.chinamobile.sparrow.domain.service.MASFacade;
import com.chinamobile.sparrow.domain.service.S3Adapter;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import javax.persistence.EntityManagerFactory;

@Configuration
@AutoConfigureAfter(value = BeanAutoConfiguration.class)
public class RepoAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public DepartmentRepository<?> departmentRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository<?> userRepository) {
        return new DepartmentRepository<>(entityManagerFactory, jinqJPAStreamProvider, Department.class, userRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public DictionaryRepository dictionaryRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider) {
        return new DictionaryRepository(entityManagerFactory, jinqJPAStreamProvider);
    }

    @Bean
    @ConditionalOnBean(value = FileSystemService.class)
    @ConditionalOnMissingBean
    public FSSRepository fssRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${file.extension.allowed}") String extensionsAllow, @Value(value = "${file.extension.forbidden}") String extensionsForbid, @Value(value = "${file.dir.env}") String env, @Value(value = "${file.dir.default}") String dir, FileSystemService fileSystemService) {
        return new FSSRepository(entityManagerFactory, jinqJPAStreamProvider, extensionsAllow, extensionsForbid, env, dir, fileSystemService);
    }

    @Bean
    @ConditionalOnMissingBean
    public LogRepository logRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository<?> userRepository) {
        return new LogRepository(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public PageRepository pageRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Lazy PermissionRepository permissionRepository) {
        return new PageRepository(entityManagerFactory, jinqJPAStreamProvider, permissionRepository);
    }

    @Bean
    @ConditionalOnBean(value = S3Adapter.class)
    @ConditionalOnMissingBean
    public OSSRepository ossRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${file.extension.allowed}") String extensionsAllow, @Value(value = "${file.extension.forbidden}") String extensionsForbid, @Value(value = "${s3.bucket.env}") String env, @Value(value = "${s3.bucket.default}") String bucket, S3Adapter s3Adapter) {
        return new OSSRepository(entityManagerFactory, jinqJPAStreamProvider, extensionsAllow, extensionsForbid, env, bucket, s3Adapter);
    }

    @Bean
    @ConditionalOnMissingBean
    public PermissionRepository permissionRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository<?> userRepository, PageRepository pageRepository) {
        return new PermissionRepository(entityManagerFactory, jinqJPAStreamProvider, userRepository, pageRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public RoleRepository roleRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository<?> userRepository, PermissionRepository permissionRepository) {
        return new RoleRepository(entityManagerFactory, jinqJPAStreamProvider, userRepository, permissionRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public SentSmsRepository sentSmsRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Autowired(required = false) MASFacade masFacade) {
        return new SentSmsRepository(entityManagerFactory, jinqJPAStreamProvider, masFacade);
    }

    @Bean
    @ConditionalOnMissingBean
    public StatisticService statisticService(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, UserRepository<?> userRepository) {
        return new StatisticService(entityManagerFactory, jinqJPAStreamProvider, userRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public UserRepository<?> userRepository(@Value(value = "${sec.password-constraint}") String passwordConstraint, @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey, @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, AbstractMediaRepository mediaRepository) {
        return new UserRepository<>(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, passwordConstraint, rsaPrivateKey, User.class);
    }

    @Bean
    @ConditionalOnMissingBean
    public VerificationCodeRepository verificationCodeRepository(@Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory, @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider, @Value(value = "${sec.sms.expires-in}") String expiresIn, @Value(value = "${sec.sms.code-length}") int codeLength, @Value(value = "${sec.sms.template}") String template, @Value(value = "${mas.sms.sign}") String sign, SentSmsRepository sentSmsRepository) {
        return new VerificationCodeRepository(entityManagerFactory, jinqJPAStreamProvider, expiresIn, codeLength, template, sign, sentSmsRepository);
    }

}