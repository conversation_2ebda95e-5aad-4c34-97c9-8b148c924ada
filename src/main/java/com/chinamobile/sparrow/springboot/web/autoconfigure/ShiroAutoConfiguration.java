package com.chinamobile.sparrow.springboot.web.autoconfigure;

import com.chinamobile.sparrow.domain.infra.sec.shiro.*;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import org.apache.shiro.authc.AuthenticationListener;
import org.apache.shiro.authc.pam.FirstSuccessfulStrategy;
import org.apache.shiro.authc.pam.ModularRealmAuthenticator;
import org.apache.shiro.authz.ModularRealmAuthorizer;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.cache.MemoryConstrainedCacheManager;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.session.SessionListenerAdapter;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.CookieRememberMeManager;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.servlet.Filter;
import java.util.*;

@Configuration
@ConditionalOnProperty(value = "shiro.enabled", matchIfMissing = true)
@AutoConfigureAfter(value = BeanAutoConfiguration.class)
public class ShiroAutoConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "shiro")
    @ConditionalOnMissingBean
    public ShiroConfigurationProperties shiroConfigurationProperties() {
        return new ShiroConfigurationProperties();
    }

    @Bean
    @ConditionalOnMissingBean
    public ConfigUserAuthorizingRealm configUserAuthorizingRealm(UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository) {
        ConfigUserAuthorizingRealm _realm = new ConfigUserAuthorizingRealm(null, userRepository, roleRepository, permissionRepository);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    public RsaCredentialsMatcher rsaCredentialsMatcher(@Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey) {
        return new RsaCredentialsMatcher(rsaPrivateKey);
    }

    @Bean
    @ConditionalOnMissingBean
    public UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm(@Value(value = "${sec.login.max-attempts}") Integer maxAttempts, UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, RsaCredentialsMatcher rsaCredentialsMatcher) {
        UsernamePasswordAuthorizingRealm _realm = new UsernamePasswordAuthorizingRealm(maxAttempts, userRepository, roleRepository, permissionRepository);
        _realm.setCredentialsMatcher(rsaCredentialsMatcher);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    @ConditionalOnMissingBean
    public SMSAuthorizingRealm smsAuthorizingRealm(@Value(value = "${sec.oauth2.client-id}") String clientId, @Value(value = "${sec.login.max-attempts}") Integer maxAttempts, UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, VerificationCodeRepository verificationCodeRepository) {
        SMSAuthorizingRealm _realm = new SMSAuthorizingRealm(clientId, maxAttempts, userRepository, roleRepository, permissionRepository, verificationCodeRepository);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    @ConditionalOnBean(value = Facade.class)
    @ConditionalOnMissingBean
    public CMPassportAuthorizingRealm cmPassportAuthorizingRealm(UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, Facade cmPassportFacade) {
        CMPassportAuthorizingRealm _realm = new CMPassportAuthorizingRealm(null, userRepository, roleRepository, permissionRepository, cmPassportFacade);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    @ConditionalOnBean(name = "wxCpAccessFacade")
    @ConditionalOnMissingBean
    public WxCpAuthorizingRealm wxCpAuthorizingRealm(UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, @Qualifier(value = "wxCpAccessFacade") AccessFacade accessFacade) {
        WxCpAuthorizingRealm _realm = new WxCpAuthorizingRealm(null, userRepository, roleRepository, permissionRepository, accessFacade);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    @ConditionalOnBean(name = "yzyAccessFacade")
    @ConditionalOnMissingBean
    public YzyAuthorizingRealm yzyAuthorizingRealm(UserRepository<?> userRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, @Qualifier(value = "yzyAccessFacade") AccessFacade accessFacade) {
        YzyAuthorizingRealm _realm = new YzyAuthorizingRealm(null, userRepository, roleRepository, permissionRepository, accessFacade);
        _realm.setAuthorizationCachingEnabled(false);
        return _realm;
    }

    @Bean
    @ConditionalOnProperty(value = "shiro.redis-session-dao.enabled", havingValue = "true")
    @ConditionalOnMissingBean
    public SessionDAO redisSessionDAO(@Value(value = "${shiro.redis-session-dao.key-template}") String keyTemplate, RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> _redisTemplate = new RedisTemplate<>();
        _redisTemplate.setConnectionFactory(redisConnectionFactory);

        _redisTemplate.setKeySerializer(new StringRedisSerializer());
        _redisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
        _redisTemplate.afterPropertiesSet();

        return new RedisSessionDAO(keyTemplate, _redisTemplate);
    }

    @Bean
    @ConditionalOnMissingBean
    public AuthenticationListener defaultAuthenticationListener(UserRepository<?> userRepository) {
        return new DefaultAuthenticationListener(userRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public ModularRealmAuthenticator authenticator(AuthenticationListener authenticationListener, ConfigUserAuthorizingRealm configUserAuthorizingRealm, UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm, SMSAuthorizingRealm smsAuthorizingRealm, @Autowired(required = false) CMPassportAuthorizingRealm cmPassportAuthorizingRealm, @Autowired(required = false) WxCpAuthorizingRealm wxCpAuthorizingRealm, @Autowired(required = false) YzyAuthorizingRealm yzyAuthorizingRealm) {
        ModularRealmAuthenticator _authenticator = new DefaultModularRealmAuthenticator(configUserAuthorizingRealm, usernamePasswordAuthorizingRealm, smsAuthorizingRealm, cmPassportAuthorizingRealm, wxCpAuthorizingRealm, yzyAuthorizingRealm);
        _authenticator.setAuthenticationStrategy(new FirstSuccessfulStrategy());
        _authenticator.setAuthenticationListeners(Collections.singletonList(authenticationListener));

        return _authenticator;
    }

    @Bean
    @ConditionalOnMissingBean
    public ModularRealmAuthorizer authorizer() {
        return new DefaultModularRealmAuthorizer();
    }

    @Bean
    @ConditionalOnMissingBean
    public SessionListenerAdapter defaultSessionListener(UserRepository<?> userRepository) {
        return new DefaultSessionListener(userRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public SessionManager sessionManager(SessionDAO sessionDAO, ShiroConfigurationProperties shiroConfigurationProperties, SessionListenerAdapter sessionListenerAdapter) {
        DefaultWebSessionManager _sessionManager = new DefaultWebSessionManager();

        // 设置cookie
        _sessionManager.setSessionIdCookie(shiroConfigurationProperties.getSessionIdCookie());
        _sessionManager.setSessionIdUrlRewritingEnabled(false);

        _sessionManager.setSessionDAO(sessionDAO);

        // 设置session过期时间
        _sessionManager.setGlobalSessionTimeout(shiroConfigurationProperties.getSession().getTimeout() * 1000);
        // 设置session验证间隔
        _sessionManager.setSessionValidationInterval(shiroConfigurationProperties.getSession().getValidationInterval() * 1000);
        // 设置session监听器
        _sessionManager.setSessionListeners(Collections.singletonList(sessionListenerAdapter));

        return _sessionManager;
    }

    @Bean
    @ConditionalOnMissingBean
    public CacheManager cacheManager() {
        return new MemoryConstrainedCacheManager();
    }

    @Bean
    @ConditionalOnMissingBean
    public RememberMeManager cookieRememberMeManager(ShiroConfigurationProperties shiroConfigurationProperties) {
        CookieRememberMeManager _cookieRememberMeManager = new CookieRememberMeManager();
        _cookieRememberMeManager.setCookie(shiroConfigurationProperties.getRememberMeCookie());

        return _cookieRememberMeManager;
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultWebSecurityManager webSecurityManager(ConfigUserAuthorizingRealm configUserAuthorizingRealm, UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm, SMSAuthorizingRealm smsAuthorizingRealm, @Autowired(required = false) CMPassportAuthorizingRealm cmPassportAuthorizingRealm, @Autowired(required = false) WxCpAuthorizingRealm wxCpAuthorizingRealm, @Autowired(required = false) YzyAuthorizingRealm yzyAuthorizingRealm, ModularRealmAuthenticator authenticator, ModularRealmAuthorizer authorizer, SessionManager sessionManager, CacheManager cacheManager, RememberMeManager rememberMeManager) {
        DefaultWebSecurityManager _manager = new DefaultWebSecurityManager();

        _manager.setAuthenticator(authenticator);

        _manager.setAuthorizer(authorizer);

        // 设置认证域
        List<Realm> _realms = new ArrayList<Realm>() {{
            add(configUserAuthorizingRealm);
            add(usernamePasswordAuthorizingRealm);
            add(smsAuthorizingRealm);
        }};

        if (cmPassportAuthorizingRealm != null) {
            _realms.add(cmPassportAuthorizingRealm);
        }
        if (wxCpAuthorizingRealm != null) {
            _realms.add(wxCpAuthorizingRealm);
        }
        if (yzyAuthorizingRealm != null) {
            _realms.add(yzyAuthorizingRealm);
        }

        _manager.setRealms(_realms);

        // 设置会话管理器
        _manager.setSessionManager(sessionManager);

        // 设置缓存管理器
        _manager.setCacheManager(cacheManager);

        // 设置记住管理器
        _manager.setRememberMeManager(rememberMeManager);

        return _manager;
    }

    @Bean
    @ConditionalOnMissingBean
    public ShiroFilterFactoryBean shiroFilterFactoryBean(@Value(value = "${sec.username}") String username, @Value(value = "${sec.login.url}") String loginUrl, SecurityManager securityManager, ShiroConfigurationProperties shiroConfigurationProperties, LoginUtil loginUtil) {
        ShiroFilterFactoryBean _filterFactory = new ShiroFilterFactoryBean();
        _filterFactory.setSecurityManager(securityManager);

        // 设置认证过滤器
        Map<String, Filter> _filters = new LinkedHashMap<>();
        _filters.put("user", new DefaultAuthenticationFilter(username, loginUrl, loginUtil));
        _filterFactory.setFilters(_filters);
        _filterFactory.setFilterChainDefinitionMap(shiroConfigurationProperties.getFilterChainDefinitionMap());
        return _filterFactory;
    }

    // 生命周期处理器
    @Bean
    @ConditionalOnMissingBean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    // AOP，依赖生命周期处理器
    @Bean
    @ConditionalOnMissingBean
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator _creator = new DefaultAdvisorAutoProxyCreator();
        _creator.setProxyTargetClass(true);

        return _creator;
    }

    // 开启注解
    @Bean
    @ConditionalOnMissingBean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor _advisor = new AuthorizationAttributeSourceAdvisor();
        _advisor.setSecurityManager(securityManager);

        return _advisor;
    }

}