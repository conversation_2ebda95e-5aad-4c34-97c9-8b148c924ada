package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.Dictionary;
import com.chinamobile.sparrow.domain.repository.sys.DictionaryRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/dictionary")
public class DictionaryController {

    protected final DictionaryRepository dictionaryRepository;
    protected final LoginUtil loginUtil;

    public DictionaryController(DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        this.dictionaryRepository = dictionaryRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "sys:dictionary:get")
    public Result<Dictionary> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Dictionary> _record = dictionaryRepository.get(_id, null);
        if(_record.isOK()) {
            dictionaryRepository.parseUsers(Collections.singletonList(_record.data));
        }

        return _record;
     }

    @PostMapping(value = "/search")
    @ResponseBody
    @RequiresPermissions(value = "sys:dictionary:search")
    public Result<PaginatedRecords<Dictionary>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _groupId = Optional.ofNullable(data.get("groupId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Boolean _isEnabled = Optional.ofNullable(data.get("isEnabled"))
                .map(i -> i.isJsonNull() ? null : i.getAsBoolean()).orElse(null);

        Result<PaginatedRecords<Dictionary>> _page = new Result<>();
        _page.data = dictionaryRepository.search(_count, _index, _sorters, _groupId, _name, _isEnabled);
        dictionaryRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "sys:dictionary:save")
    public Result<String> save(@RequestBody @Validated Dictionary record) {
        return dictionaryRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "sys:dictionary:remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return dictionaryRepository.remove(_id);
    }

}