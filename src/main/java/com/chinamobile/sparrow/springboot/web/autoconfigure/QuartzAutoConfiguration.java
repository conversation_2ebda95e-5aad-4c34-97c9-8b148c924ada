package com.chinamobile.sparrow.springboot.web.autoconfigure;

import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.quartz.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Objects;

@Configuration
public class QuartzAutoConfiguration {

    @Bean
    @ConditionalOnBean(name = "quartzDataSource")
    @ConditionalOnMissingBean
    public SchedulerFactoryBeanCustomizer dataSourceCustomizer(QuartzProperties properties, @QuartzDataSource ObjectProvider<DataSource> quartzDataSource, ObjectProvider<PlatformTransactionManager> transactionManager) {
        return (schedulerFactoryBean) -> {
            if (properties.getJobStoreType() == JobStoreType.JDBC) {
                schedulerFactoryBean.setDataSource(Objects.requireNonNull(quartzDataSource.getIfAvailable()));
                PlatformTransactionManager txManager = transactionManager.getIfUnique();
                if (txManager != null) {
                    schedulerFactoryBean.setTransactionManager(txManager);
                }
            }
        };
    }

    @Bean
    @ConditionalOnMissingBean
    public QuartzDataSourceInitializer quartzDataSourceInitializer(@QuartzDataSource ObjectProvider<DataSource> quartzDataSource, ResourceLoader resourceLoader, QuartzProperties properties) {
        return new QuartzDataSourceInitializer(quartzDataSource.getIfAvailable(), resourceLoader, properties);
    }

}