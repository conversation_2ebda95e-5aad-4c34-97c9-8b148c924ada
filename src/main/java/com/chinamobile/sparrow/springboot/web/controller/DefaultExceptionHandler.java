package com.chinamobile.sparrow.springboot.web.controller;

import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.ResultWarpperRuntimeException;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.orm.hibernate5.HibernateJdbcException;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;
import java.sql.SQLSyntaxErrorException;

@ControllerAdvice
public class DefaultExceptionHandler {

    protected final DefaultResultParser resultParser;

    public DefaultExceptionHandler(DefaultResultParser resultParser) {
        this.resultParser = resultParser;
    }

    @ExceptionHandler(value = ResultWarpperRuntimeException.class)
    @ResponseBody
    public Result<?> handle(HttpServletRequest request, ResultWarpperRuntimeException e) throws Throwable {
        preHandle(request, e);

        return e.getResult();
    }

    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseBody
    public Result<Void> handle(HttpServletRequest request, ConstraintViolationException e) throws Throwable {
        preHandle(request, e);

        SQLException _exception = e.getSQLException();

        Result<Void> _fail = new Result<>();
        _fail.setCode(Result.DATABASE_UNKNOWN, new Object[]{_exception.getMessage()});
        return _fail;
    }

    @ExceptionHandler(value = HibernateJdbcException.class)
    @ResponseBody
    public Result<Void> handle(HttpServletRequest request, HibernateJdbcException e) throws Throwable {
        preHandle(request, e);

        SQLException _exception = e.getSQLException();

        Result<Void> _fail = new Result<>();
        _fail.setCode(Result.DATABASE_UNKNOWN, new Object[]{_exception.getMessage()});
        return _fail;
    }

    @ExceptionHandler(value = SQLSyntaxErrorException.class)
    @ResponseBody
    public Result<Void> handle(HttpServletRequest request, SQLSyntaxErrorException e) throws Throwable {
        preHandle(request, e);

        Result<Void> _fail = new Result<>();
        _fail.setCode(Result.DATABASE_UNKNOWN, new Object[]{e.getMessage()});
        return _fail;
    }

    public void preHandle(HttpServletRequest request, Throwable e) throws Throwable {
        String _accept = request.getHeader(HttpHeaders.ACCEPT);
        if (StringUtils.hasLength(_accept) && _accept.contains(MediaType.TEXT_HTML_VALUE)) {
            throw e;
        }
    }

}