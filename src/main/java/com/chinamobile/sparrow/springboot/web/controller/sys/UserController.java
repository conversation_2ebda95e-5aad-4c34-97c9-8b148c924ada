package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/user")
public class UserController {

    protected final UserRepository userRepository;
    protected final LoginUtil loginUtil;

    public UserController(UserRepository userRepository, LoginUtil loginUtil) {
        this.userRepository = userRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:get")
    public Result<User> get(@RequestBody JsonObject data) throws InstantiationException, IllegalAccessException {
        String _id = data.get("id").getAsString();

        return userRepository.getByIdOrAccountOrMp(_id, null);
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:save")
    public Result<String> save(@RequestBody User record) throws InstantiationException, IllegalAccessException {
        return userRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/password/change")
    @ResponseBody
    public Result<Void> changePassword(@RequestBody JsonObject data) {
        String _originalPassword = data.get("originalPassword").getAsString();
        String _newPassword = data.get("newPassword").getAsString();

        return userRepository.changePassword(loginUtil.getUserId(), _originalPassword, _newPassword, loginUtil.getUserId());
    }

    @PostMapping(value = "/password/reset")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:reset-password")
    public Result<Void> updatePassword(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _password = data.get("password").getAsString();

        return userRepository.resetPassword(_id, _password, loginUtil.getUserId());
    }

    @PostMapping(value = "/disable")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:disable")
    public Result<Void> disable(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return userRepository.enable(_id, false, loginUtil.getUserId());
    }

    @PostMapping(value = "/enable")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:enable")
    public Result<Void> enable(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return userRepository.enable(_id, true, loginUtil.getUserId());
    }

    @PostMapping(value = "/lock")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:lock")
    public Result<Void> lock(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return userRepository.lock(_id, true, loginUtil.getUserId());
    }

    @PostMapping(value = "/unlock")
    @ResponseBody
    @RequiresPermissions(value = "sys:user:unlock")
    public Result<Void> unlock(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return userRepository.lock(_id, false, loginUtil.getUserId());
    }

    @PostMapping(value = "/suggest")
    @ResponseBody
    public Result<List<User>> getUsers(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        String _rootDeptId = Optional.ofNullable(data.get("rootDeptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _key = data.get("key").getAsString();

        Result<List<User>> _page = new Result<>();
        _page.data = userRepository.suggest(_count, _rootDeptId, _key);
        for (User i : _page.data) {
            i = userRepository.mask(i);
        }
        return _page;
    }

}