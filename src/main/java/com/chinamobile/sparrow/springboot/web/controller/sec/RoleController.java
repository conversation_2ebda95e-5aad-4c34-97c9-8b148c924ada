package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sec/role")
public class RoleController {

    protected final RoleRepository roleRepository;
    protected final PermissionRepository permissionRepository;
    protected final LoginUtil loginUtil;

    public RoleController(RoleRepository roleRepository, PermissionRepository permissionRepository, LoginUtil loginUtil) {
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/get")
    @ResponseBody
    @RequiresPermissions(value = "sys:role:get")
    public Result<Role> get(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<Role> _record = roleRepository.getById(_id);
        if (_record.isOK()) {
            roleRepository.parseUsers(Collections.singletonList(_record.data));
        }

        return _record;
    }

    @PostMapping(value = "/search")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:search")
    public Result<PaginatedRecords<Role>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<Role>> _page = new Result<>();
        _page.data = roleRepository.search(_count, _index, _sorters, _name);
        roleRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/me")
    @ResponseBody
    public Result<List<Role>> me() {
        Result<List<Role>> _roles = new Result<>();
        _roles.data = roleRepository.findUserRoles(loginUtil.getUserId());
        return _roles;
    }

    @PostMapping(value = "/save")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:save")
    public Result<String> save(@RequestBody @Validated Role record) {
        return roleRepository.save(record, loginUtil.getUserId());
    }

    @PostMapping(value = "/remove")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:remove")
    public Result<Void> remove(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return roleRepository.remove(_id);
    }

    @PostMapping(value = "/user/find")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:user:find")
    public Result<PaginatedRecords<User>> findUsers(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _id = data.get("id").getAsString();
        String _account = Optional.ofNullable(data.get("account"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _username = Optional.ofNullable(data.get("username"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<User>> _users = new Result<>();
        _users.data = roleRepository.findUsers(_count, _index, _sorters, _id, _account, _username, _mp);
        return _users;
    }

    @PostMapping(value = "/user/add")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:user:add")
    public Result<Void> addUser(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _userId = data.get("userId").getAsString();

        return roleRepository.addUser(_id, _userId, loginUtil.getUserId());
    }

    @PostMapping(value = "/user/remove")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:user:remove")
    public Result<Void> removeUsers(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        String _userId = data.get("userId").getAsString();

        return roleRepository.removeUser(_id, _userId);
    }

    @PostMapping(value = "/permission/find")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:permission:find")
    public Result<List<Permission>> findPermissions(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        Result<List<Permission>> _permissions = new Result<>();
        _permissions.data = roleRepository.findPermissions(_id);
        return _permissions;
    }

    @PostMapping(value = "/permission/save")
    @ResponseBody
    @RequiresPermissions(value = "sec:role:permission:save")
    public Result<Void> setPermissions(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();
        List<String> _permissionIds = ConverterUtil.json2Object(data.get("permissionIds").toString(), new TypeToken<List<String>>() {
        }.getType());

        return roleRepository.setPermissions(_id, _permissionIds, loginUtil.getUserId());
    }

}