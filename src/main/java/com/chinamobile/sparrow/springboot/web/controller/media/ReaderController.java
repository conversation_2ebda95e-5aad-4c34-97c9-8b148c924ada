package com.chinamobile.sparrow.springboot.web.controller.media;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.media.Media;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;

@Controller
@RequestMapping(value = "media")
public class ReaderController {

    protected final AbstractMediaRepository mediaRepository;
    protected final RecordController recordController;
    protected final LoginUtil loginUtil;

    public ReaderController(AbstractMediaRepository mediaRepository, RecordController recordController, LoginUtil loginUtil) {
        this.mediaRepository = mediaRepository;
        this.recordController = recordController;
        this.loginUtil = loginUtil;
    }

    @RequestMapping(value = "/read", method = RequestMethod.GET)
    public void read(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "id") String id) throws Exception {
        writeToResponse(request, response, id);
    }

    @RequestMapping(value = "/read/force-partial-content", method = RequestMethod.GET)
    public void readPart(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "id") String id) throws Exception {
        String _range = request.getHeader(HttpHeaders.RANGE);
        if (_range != null && _range.startsWith("bytes=")) {
            writeToResponse(request, response, id);
            return;
        }

        Result<Media> _record = mediaRepository.get(id, loginUtil.getUserId());
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        Result<Long> _length = mediaRepository.getContentLength(_record.data);
        if (!_length.isOK()) {
            throw new Exception(_length.message);
        }

        response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
        response.setHeader(HttpHeaders.ETAG, id);
        response.setHeader(HttpHeaders.LAST_MODIFIED, new Date().toString());
        response.setHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(_length.data));
    }

    public void writeToResponse(HttpServletRequest request, HttpServletResponse response, String id) throws Exception {
        String _range = request.getHeader(HttpHeaders.RANGE);
        if (_range == null || !_range.startsWith("bytes=")) {
            recordController.writeToResponse(response, id, false);
            return;
        }

        String[] _values = _range.split("=")[1].split("-");
        Long _start = StringUtils.hasLength(_values[0]) ? Long.parseLong(_values[0]) : 0L;
        Long _end = _values.length > 1 && StringUtils.hasLength(_values[1]) ? Long.parseLong(_values[1]) : null;

        Result<Media> _record = mediaRepository.get(id, loginUtil.getUserId());
        if (!_record.isOK()) {
            throw new Exception(_record.message);
        }

        Result<Long> _length = mediaRepository.getContentLength(_record.data);
        if (!_length.isOK()) {
            throw new Exception(_length.message);
        }

        Result<byte[]> _bytes = mediaRepository.getPart(_record.data, _start, _end);
        if (!_bytes.isOK()) {
            throw new Exception(_bytes.message);
        }

        response.setStatus(HttpServletResponse.SC_PARTIAL_CONTENT);
        response.setHeader(HttpHeaders.ACCEPT_RANGES, "bytes");
        response.setHeader(HttpHeaders.CONTENT_LENGTH, String.valueOf(_bytes.data.length));
        response.setHeader(HttpHeaders.CONTENT_RANGE, String.format("bytes %s-%s/%s", _start, _start + _bytes.data.length - 1, _length.data));
        response.setHeader(HttpHeaders.ETAG, _record.data.getId());
        response.setHeader(HttpHeaders.LAST_MODIFIED, new Date().toString());

        FileCopyUtils.copy(_bytes.data, response.getOutputStream());
    }

}