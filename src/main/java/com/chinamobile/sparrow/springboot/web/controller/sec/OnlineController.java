package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sec/online")
public class OnlineController {

    protected final UserRepository userRepository;
    protected final LoginUtil loginUtil;

    public OnlineController(UserRepository userRepository, LoginUtil loginUtil) {
        this.userRepository = userRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/search")
    @ResponseBody
    @RequiresPermissions(value = "sec:online:search")
    public Result<PaginatedRecords<User>> searchOnline(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String account = Optional.ofNullable(data.get("account"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _deptIid = Optional.ofNullable(data.get("deptId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<PaginatedRecords<User>> _page = new Result<>();
        _page.data = userRepository.searchOnline(_count, _index, _sorters, account, _deptIid, _name, _mp);
        return _page;
    }

    @PostMapping(value = "/logout")
    @ResponseBody
    @RequiresPermissions(value = "sec:online:logout")
    public Result<Void> logout(@RequestBody JsonObject data) {
        Result<Void> _success = new Result<>();

        String _id = data.get("id").getAsString();
        Result<User> _record = userRepository.getBriefByIdOrAccountOrMp(_id, true);
        if (!_record.isOK()) {
            return _success.pack(_record);
        }

        loginUtil.logout(_record.data);

        return _success;
    }

}