package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.repository.sys.StatisticService;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jinq.tuples.Pair;
import org.jinq.tuples.Tuple3;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.*;

@Controller
@RequestMapping(value = "sys/stat")
public class StatisticController {

    protected final UserRepository userRepository;
    protected final StatisticService statisticService;

    public StatisticController(UserRepository userRepository, StatisticService statisticService) {
        this.userRepository = userRepository;
        this.statisticService = statisticService;
    }

    @PostMapping(value = "/operation")
    @ResponseBody
    @NotLog
    public Result<OperationIndicator> operation() {
        Result<OperationIndicator> _num = new Result<>();

        _num.data = new OperationIndicator();
        _num.data.setRu(userRepository.registeredUserNum());
        _num.data.setCu(userRepository.onlineUserNum());
        _num.data.setDar(statisticService.dar(null, null, null).stream()
                .map(Pair::getTwo)
                .findFirst().orElse(BigDecimal.ZERO));
        _num.data.setUrr(statisticService.urr(null, null, Calendar.DATE));

        return _num;
    }

    @PostMapping(value = "/dau")
    @ResponseBody
    @RequiresPermissions(value = "sys:stat:dau")
    public Result<List<Pair<Date, Long>>> dau(@RequestBody JsonObject data) {
        Date _startDate = Optional.ofNullable(data.get("startDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        Date _endDate = Optional.ofNullable(data.get("endDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        String _method = Optional.ofNullable(data.get("method"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);
        boolean _individual = Optional.ofNullable(data.get("individual"))
                .map(i -> i.isJsonNull() || i.getAsBoolean())
                .orElse(true);

        Result<List<Pair<Date, Long>>> _nums = new Result<>();
        _nums.data = statisticService.dau(_startDate, _endDate, _method, _individual);
        return _nums;
    }

    @PostMapping(value = "/dar")
    @ResponseBody
    @RequiresPermissions(value = "sys:stat:dar")
    public Result<List<Pair<Date, BigDecimal>>> dar(@RequestBody JsonObject data) {
        Date _startDate = Optional.ofNullable(data.get("startDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        Date _endDate = Optional.ofNullable(data.get("endDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        String _method = Optional.ofNullable(data.get("method"))
                .map(i -> i.isJsonNull() ? null : i.getAsString())
                .orElse(null);

        Result<List<Pair<Date, BigDecimal>>> _nums = new Result<>();
        _nums.data = statisticService.dar(_startDate, _endDate, _method);
        return _nums;
    }

    @PostMapping(value = "/urr")
    @ResponseBody
    @RequiresPermissions(value = "sys:stat:urr")
    public Result<BigDecimal> uur(@RequestBody JsonObject data) {
        int _field = Optional.ofNullable(data.get("field"))
                .map(i -> i.isJsonNull() ? Calendar.DATE : i.getAsInt())
                .orElse(Calendar.MONTH);

        Result<BigDecimal> _uur = new Result<>();
        _uur.data = statisticService.urr(null, null, _field);
        return _uur;
    }

    @PostMapping(value = "/trend")
    @ResponseBody
    @RequiresPermissions(value = "sys:stat:trend")
    public Result<List<Pair<Integer, Long>>> trend(@RequestBody JsonObject data) {
        Date _startDate = Optional.ofNullable(data.get("startDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _endDate = Optional.ofNullable(data.get("endDate"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        String _method = Optional.ofNullable(data.get("method"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);

        Result<List<Pair<Integer, Long>>> _nums = new Result<>();
        _nums.data = statisticService.trend(_startDate, _endDate, _method);
        return _nums;
    }

    @PostMapping(value = "/mru")
    @ResponseBody
    @RequiresPermissions(value = "sys:stat:mru")
    public Result<List<MRU>> mru(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        Date _date = Optional.ofNullable(data.get("date"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd"))
                .orElse(null);
        int _field = Optional.ofNullable(data.get("field"))
                .filter(i -> !i.isJsonNull())
                .map(JsonElement::getAsInt)
                .orElse(Calendar.DATE);

        Result<List<MRU>> _nums = new Result<>();
        _nums.data = new ArrayList<>();

        for (Tuple3<String, Long, Long> i : statisticService.mru(_count, _date, _field)) {
            _nums.data.add(new MRU(i.getOne(), i.getTwo(), i.getThree()));
        }

        return _nums;
    }

    public static class OperationIndicator {

        // 注册用户数量
        long ru;
        // 在线用户数量
        long cu;
        // 日活跃率
        BigDecimal dar;
        // 日留存率
        BigDecimal urr;

        public long getRu() {
            return ru;
        }

        public void setRu(long ru) {
            this.ru = ru;
        }

        public long getCu() {
            return cu;
        }

        public void setCu(long cu) {
            this.cu = cu;
        }

        public BigDecimal getDar() {
            return dar;
        }

        public void setDar(BigDecimal dar) {
            this.dar = dar;
        }

        public BigDecimal getUrr() {
            return urr;
        }

        public void setUrr(BigDecimal urr) {
            this.urr = urr;
        }

    }

    public static class MRU {

        String method;
        Long users;
        Long visits;

        public MRU() {
        }

        public MRU(String method, Long users, Long visits) {
            this.method = method;
            this.users = users;
            this.visits = visits;
        }

        public String getMethod() {
            return method;
        }

        public void setMethod(String method) {
            this.method = method;
        }

        public Long getUsers() {
            return users;
        }

        public void setUsers(Long users) {
            this.users = users;
        }

        public Long getVisits() {
            return visits;
        }

        public void setVisits(Long visits) {
            this.visits = visits;
        }

    }

}