package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.model.sec.Role;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.model.sys.Page;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.util.CryptoUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Controller
@RequestMapping(value = "")
public class SettingController {

    protected final String rsaPublicKey;
    protected final UserRepository userRepository;
    protected final DepartmentRepository departmentRepository;
    protected final RoleRepository roleRepository;
    protected final PermissionRepository permissionRepository;
    protected final PageRepository pageRepository;
    protected final LoginUtil loginUtil;

    public SettingController(String rsaPublicKey, UserRepository userRepository, DepartmentRepository departmentRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, PageRepository pageRepository, LoginUtil loginUtil) {
        this.rsaPublicKey = rsaPublicKey;
        this.userRepository = userRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
        this.permissionRepository = permissionRepository;
        this.pageRepository = pageRepository;
        this.loginUtil = loginUtil;
    }

    @RequestMapping(value = "/app/init")
    @ResponseBody
    public Result<Void> init() throws Exception {
        User _user = null;
        String _password = "********";

        if (userRepository.registeredUserNum() == 0) {
            // 新增部门
            Department _department = new Department();
            _department.setName("默认");
            _department.setCode(_department.getId());
            _department.setFullName(_department.getName());
            _department.setLevel(1);
            departmentRepository.add(_department, null);

            // 新增人员
            _user = new User();
            _user.setAccount("admin");
            _user.setPassword(CryptoUtil.encryptRsa(_password, rsaPublicKey));
            _user.setName("系统管理员");
            _user.setDeptId(_department.getId());
            _user.setIsMale(true);
            userRepository.add(_user, null);
        }

        pageRepository.renew();

        if (!roleRepository.getByName("系统管理员").isOK()) {
            // 新增角色
            Role _role = new Role();
            _role.setName("系统管理员");
            roleRepository.add(_role, null);

            // 授权
            List<String> _permissionIds = permissionRepository.find(null).stream()
                    .filter(i -> i.getLevel() == 1)
                    .map(Permission::getId)
                    .collect(Collectors.toList());
            roleRepository.setPermissions(_role.getId(), _permissionIds, null);

            // 任命
            if (_user != null) {
                roleRepository.addUser(_role.getId(), _user.getId(), null);
            }
        }

        return new Result<>();
    }

    @PostMapping(value = "/app/layout/header")
    @NotLog
    @ResponseBody
    public Result<Header> header(@Value(value = "${spring.application.name}") String app, @RequestBody JsonObject data) {
        Result<Header> _header = new Result<>();
        _header.data = new Header();

        // 设置应用名称
        _header.data.setApp(app);

        if (SecurityUtils.getSubject().getPrincipal() != null) {
            // 设置用户
            _header.data.setUser(loginUtil.getUserMasked());

            // 设置菜单
            List<Page> _pages = pageRepository.find();
            if (data.get("root").getAsBoolean()) {
                _pages = _pages.stream()
                        .filter(i -> i.getLevel() == 1)
                        .collect(Collectors.toList());
            }
            _header.data.setPages(_pages);
        }

        return _header;
    }

    @PostMapping(value = "/app/layout/menu")
    @NotLog
    @ResponseBody
    public Result<List<Page>> menu() {
        Result<List<Page>> _menu = new Result<>();
        _menu.data = pageRepository.find();
        return _menu;
    }

    @PostMapping(value = "/app/layout/user")
    @NotLog
    @ResponseBody
    public Result<User> user() {
        Result<User> _user = new Result<>();
        _user.data = loginUtil.getUserMasked();

        return _user;
    }

    @PostMapping(value = "/app/layout/badge")
    @NotLog
    @ResponseBody
    public Result<List<Badge>> badge() {
        Result<List<Badge>> _badge = new Result<>();
        _badge.data = new ArrayList<>();
        return _badge;
    }

    public static class Header {

        String app;
        User user;
        List<Page> pages;

        public Header() {
        }

        public String getApp() {
            return app;
        }

        public void setApp(String app) {
            this.app = app;
        }

        public User getUser() {
            return user;
        }

        public void setUser(User user) {
            this.user = user;
        }

        public List<Page> getPages() {
            return pages;
        }

        public void setPages(List<Page> pages) {
            this.pages = pages;
        }

    }

    public static class Badge {

        String path;
        Long count;

        public Badge(String path, Long count) {
            this.path = path;
            this.count = count;
        }

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

    }

}