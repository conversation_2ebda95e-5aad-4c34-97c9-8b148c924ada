package com.chinamobile.sparrow.springboot.web.controller;

import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.code.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

// 全局异常处理器
@Controller
public class ErrorController extends BasicErrorController {

    protected final DefaultResultParser resultParser;
    protected final Logger logger;

    public ErrorController(DefaultErrorAttributes errorAttributes, ServerProperties serverProperties, DefaultResultParser resultParser) {
        super(errorAttributes, serverProperties.getError());
        this.resultParser = resultParser;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    @Override
    public ResponseEntity<Map<String, Object>> error(HttpServletRequest request) {
        Map<String, Object> _model = super.getErrorAttributes(request, this.getErrorAttributeOptions(request, MediaType.TEXT_HTML));
        Map<String, Object> _model2 = parse(_model);
        return new ResponseEntity<>(_model2, this.getStatus(request));
    }

    protected Map<String, Object> parse(Map<String, Object> model) {
        HashMap<String, Object> _model = new HashMap<>();

        if (model.containsKey("exception")) {
            Result<Void> _fail = null;

            Class<?> _exception = null;
            try {
                _exception = Class.forName(model.get("exception").toString());
            } catch (ClassNotFoundException e) {
                logger.error(e.getMessage(), e);
            }

            // 数据验证异常
            if (_exception != null && BindException.class.isAssignableFrom(_exception) && model.get("errors") instanceof List) {
                List<?> _errors = (List<?>) model.get("errors");

                for (Object i : _errors) {
                    if (i instanceof FieldError) {
                        FieldError _error = (FieldError) i;
                        _fail = new Result<>();
                        _fail.setCode(Result.DATA_VALIDATE_ERROR, new Object[]{_error.getField(), _error.getDefaultMessage(), _error.getRejectedValue()});

                        break;
                    }
                }
            }

            if (_fail == null) {
                _fail = resultParser.fromException(Optional.ofNullable(model.get("exception"))
                        .map(Object::toString).orElse(null), true);
            }

            if (!Result.UNKNOWN.equals(_fail.getCode().split("_", 2)[1])) {
                _model.put("code", _fail.getCode());
                _model.put("message", StringUtils.hasLength(_fail.message) ? _fail.message : Optional.ofNullable(model.get("message"))
                        .map(Object::toString).orElse(""));
                return _model;
            }
        }

        String _msg = Optional.ofNullable(model.get("message"))
                .map(Object::toString).orElse(null);
        if (!StringUtils.hasLength(_msg) || "No message available".equals(_msg)) {
            _msg = Optional.ofNullable(model.get("exception"))
                    .map(Object::toString).orElse(null);
        }
        if (!StringUtils.hasLength(_msg)) {
            _msg = Optional.ofNullable(model.get("error"))
                    .map(Object::toString).orElse("");
        }

        _model.put("code", model.get("status"));
        _model.put("message", _msg);
        return _model;
    }

}