package com.chinamobile.sparrow.springboot.web.autoconfigure;

import com.chinamobile.sparrow.domain.infra.code.DefaultResultParser;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.*;
import com.chinamobile.sparrow.domain.repository.sys.log.LogRepository;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import com.chinamobile.sparrow.domain.service.QuartzFacade;
import com.chinamobile.sparrow.domain.service.cmpassport.Facade;
import com.chinamobile.sparrow.domain.service.wx.cp.AccessFacade;
import com.chinamobile.sparrow.springboot.web.controller.DefaultExceptionHandler;
import com.chinamobile.sparrow.springboot.web.controller.ErrorController;
import com.chinamobile.sparrow.springboot.web.controller.UtilController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.OnlineController;
import com.chinamobile.sparrow.springboot.web.controller.sec.PermissionController;
import com.chinamobile.sparrow.springboot.web.controller.sec.RoleController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController;
import org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@AutoConfigureAfter(value = BeanAutoConfiguration.class)
@AutoConfigureBefore(value = ErrorMvcAutoConfiguration.class)
public class CtrlAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    public DepartmentController departmentController(DepartmentRepository<?> departmentRepository, LoginUtil loginUtil) {
        return new DepartmentController(departmentRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public DictionaryController dictionaryController(DictionaryRepository dictionaryRepository, LoginUtil loginUtil) {
        return new DictionaryController(dictionaryRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public BasicErrorController errorController(DefaultErrorAttributes errorAttributes, ServerProperties serverProperties, DefaultResultParser resultParser) {
        return new ErrorController(errorAttributes, serverProperties, resultParser);
    }

    @Bean
    @ConditionalOnBean(value = QuartzFacade.class)
    @ConditionalOnMissingBean
    public JobController jobController(QuartzFacade quartzFacade) {
        return new JobController(quartzFacade);
    }

    @Bean
    @ConditionalOnMissingBean
    public LogController logController(LogRepository logRepository) {
        return new LogController(logRepository);
    }

    @Bean
    @ConditionalOnMissingBean
    public LoginController loginController(@Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey, @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey, VerificationCodeRepository verificationCodeRepository, @Autowired(required = false) Facade cmPassportFacade, @Autowired(required = false) AccessFacade wxCpAccessFacade, @Autowired(required = false) AccessFacade yzyAccessFacade, LoginUtil loginUtil) {
        return new LoginController(rsaPublicKey, rsaPrivateKey, verificationCodeRepository, cmPassportFacade, wxCpAccessFacade, yzyAccessFacade, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public RecordController mediaController(AbstractMediaRepository mediaRepository, LoginUtil loginUtil) {
        return new RecordController(mediaRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public OnlineController onlineController(UserRepository<?> userRepository, LoginUtil loginUtil) {
        return new OnlineController(userRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public PermissionController permissionController(PermissionRepository permissionRepository, LoginUtil loginUtil) {
        return new PermissionController(permissionRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ProfileController profileController(UserRepository<?> userRepository, LoginUtil loginUtil) {
        return new ProfileController(userRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public ReaderController readerController(AbstractMediaRepository mediaRepository, RecordController recordController, LoginUtil loginUtil) {
        return new ReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public RoleController roleController(RoleRepository roleRepository, PermissionRepository permissionRepository, LoginUtil loginUtil) {
        return new RoleController(roleRepository, permissionRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public SentSmsController sentSmsController(SentSmsRepository sentSmsRepository, LoginUtil loginUtil) {
        return new SentSmsController(sentSmsRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public SettingController settingController(@Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey, UserRepository<?> userRepository, DepartmentRepository<?> departmentRepository, RoleRepository roleRepository, PermissionRepository permissionRepository, PageRepository pageRepository, LoginUtil loginUtil) {
        return new SettingController(rsaPublicKey, userRepository, departmentRepository, roleRepository, permissionRepository, pageRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public StatisticController statisticController(UserRepository<?> userRepository, StatisticService statisticService) {
        return new StatisticController(userRepository, statisticService);
    }

    @Bean
    @ConditionalOnMissingBean
    public UserController userController(UserRepository<?> userRepository, LoginUtil loginUtil) {
        return new UserController(userRepository, loginUtil);
    }

    @Bean
    @ConditionalOnMissingBean
    public UtilController utilController(@Value(value = "${cmpassport.app-id}") String cmpassportAppId, @Value(value = "${amap.js.key}") String aMapJsKey, @Value(value = "${amap.js.code}") String aMapJsCode, @Value(value = "${qqmap.js-key}") String qqMapJsKey) {
        return new UtilController(cmpassportAppId, aMapJsKey, aMapJsCode, qqMapJsKey);
    }

    @Bean
    @ConditionalOnMissingBean
    public DefaultExceptionHandler controllerExceptionHandler(DefaultResultParser resultParser) {
        return new DefaultExceptionHandler(resultParser);
    }

}