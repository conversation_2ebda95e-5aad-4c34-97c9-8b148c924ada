package com.chinamobile.sparrow.springboot.web;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.GsonHttpMessageConverter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

@Configuration
public class WebMvcConfigurerImpl implements WebMvcConfigurer {

    final GsonHttpMessageConverter gsonHttpMessageConverter;

    public WebMvcConfigurerImpl(GsonHttpMessageConverter gsonHttpMessageConverter) {
        this.gsonHttpMessageConverter = gsonHttpMessageConverter;
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/css/**").addResourceLocations("classpath:/static/css/");
        registry.addResourceHandler("/img/**").addResourceLocations("classpath:/static/img/");
        registry.addResourceHandler("/js/**").addResourceLocations("classpath:/static/js/");
        registry.addResourceHandler("/view/**").addResourceLocations("classpath:/static/view/");
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(gsonHttpMessageConverter);
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.setUseTrailingSlashMatch(true);
        configurer.setUseSuffixPatternMatch(false);
    }

    @Bean
    @ConfigurationProperties(prefix = "web.cors")
    public CorsConfiguration corsConfiguration() {
        return new CorsConfiguration();
    }

    @Bean(name = "corsFilterFilterRegistrationBean")
    @ConditionalOnMissingBean(name = "corsFilterFilterRegistrationBean")
    public FilterRegistrationBean<CorsFilter> corsFilterFilterRegistrationBean(CorsConfiguration corsConfiguration) {
        UrlBasedCorsConfigurationSource _source = new UrlBasedCorsConfigurationSource();
        _source.registerCorsConfiguration("/**", corsConfiguration);

        FilterRegistrationBean<CorsFilter> _filterRegistrationBean = new FilterRegistrationBean<>(new CorsFilter(_source));
        _filterRegistrationBean.setOrder(0);
        return _filterRegistrationBean;
    }

}