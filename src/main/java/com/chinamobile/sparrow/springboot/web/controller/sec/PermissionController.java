package com.chinamobile.sparrow.springboot.web.controller.sec;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sec.Permission;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value = "sec/permission")
public class PermissionController {

    protected final PermissionRepository permissionRepository;
    protected final LoginUtil loginUtil;

    public PermissionController(PermissionRepository permissionRepository, LoginUtil loginUtil) {
        this.permissionRepository = permissionRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/find")
    @ResponseBody
    @RequiresPermissions(value = "sec:permission:find")
    public Result<List<Permission>> find() {
        Result<List<Permission>> _records = new Result<>();
        _records.data = permissionRepository.find(null);
        return _records;
    }

    @PostMapping(value = "/me")
    @ResponseBody
    public Result<List<Permission>> me() {
        Result<List<Permission>> _records = new Result<>();
        _records.data = permissionRepository.findUserPermissions(loginUtil.getUserId());
        return _records;
    }

    @PostMapping(value = "/renew")
    @ResponseBody
    @RequiresPermissions(value = "sec:permission:renew")
    public Result<Void> renew() throws NoSuchFieldException, IllegalAccessException {
        permissionRepository.renew(loginUtil.getUserId());

        return new Result<>();
    }

}