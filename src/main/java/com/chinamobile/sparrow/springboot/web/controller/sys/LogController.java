package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.log.AbstractLog;
import com.chinamobile.sparrow.domain.infra.log.NotLog;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.ErrorLog;
import com.chinamobile.sparrow.domain.model.sys.InfoLog;
import com.chinamobile.sparrow.domain.repository.sys.log.LogRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/log")
public class LogController {

    protected final LogRepository logRepository;

    public LogController(LogRepository logRepository) {
        this.logRepository = logRepository;
    }

    @PostMapping(value = "/search/{type}")
    @NotLog
    @ResponseBody
    @RequiresPermissions(value = "sys:log:search")
    public Result<PaginatedRecords<AbstractLog>> log(@PathVariable(value = "type") String type, @RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sortFields = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _method = Optional.ofNullable(data.get("method"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        List<String> _methods = StringUtils.hasLength(_method) ? Collections.singletonList(_method) : null;
        String _code = Optional.ofNullable(data.get("code"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _account = Optional.ofNullable(data.get("account"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _ip = Optional.ofNullable(data.get("ip"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _realmType = Optional.ofNullable(data.get("realmType"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _startTime = Optional.ofNullable(data.get("startTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH"))
                .orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH"))
                .orElse(null);

        Class _class = "error".equalsIgnoreCase(type) ? ErrorLog.class : InfoLog.class;

        Result<PaginatedRecords<AbstractLog>> _page = new Result<>();
        _page.data = logRepository.search(_class, _count, _index, _sortFields, _methods, _code, _account, _ip, _realmType, _startTime, _endTime);
        return _page;
    }

}