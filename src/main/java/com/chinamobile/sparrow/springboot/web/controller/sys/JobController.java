package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.QuartzFacade;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.quartz.SchedulerException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

@Controller
@RequestMapping(value = "sys/job")
public class JobController {

    protected final QuartzFacade quartzFacade;

    public JobController(QuartzFacade quartzFacade) {
        this.quartzFacade = quartzFacade;
    }

    @PostMapping(value = "/find")
    @ResponseBody
    @RequiresPermissions(value = "sys:job:find")
    public Result<List<QuartzFacade.JobVO>> find() throws SchedulerException {
        Result<List<QuartzFacade.JobVO>> _page = new Result<>();
        _page.data = quartzFacade.findJobs();
        return _page;
    }

    @PostMapping(value = "/update-cron")
    @ResponseBody
    @RequiresPermissions(value = "sys:job:update")
    public Result<Void> updateCron(@RequestBody JsonObject data) throws SchedulerException {
        String _name = data.get("name").getAsString();
        String _group = data.get("group").getAsString();
        String _cron = data.get("cron").getAsString();

        quartzFacade.updateCron(_name, _group, _cron);

        return new Result<>();
    }

    @PostMapping(value = "/trigger")
    @ResponseBody
    @RequiresPermissions(value = "sys:job:trigger")
    public Result<Void> trigger(@RequestBody JsonObject data) throws SchedulerException {
        String _name = data.get("name").getAsString();
        String _group = data.get("group").getAsString();

        quartzFacade.triggerJob(_name, _group);

        return new Result<>();
    }

    @PostMapping(value = "/pause")
    @ResponseBody
    @RequiresPermissions(value = "sys:job:pause")
    public Result<Void> pause(@RequestBody JsonObject data) throws SchedulerException {
        String _name = data.get("name").getAsString();
        String _group = data.get("group").getAsString();

        quartzFacade.pauseJob(_name, _group);

        return new Result<>();
    }

    @PostMapping(value = "/resume")
    @ResponseBody
    @RequiresPermissions(value = "sys:job:resume")
    public Result<Void> resume(@RequestBody JsonObject data) throws SchedulerException {
        String _name = data.get("name").getAsString();
        String _group = data.get("group").getAsString();

        quartzFacade.resumeJob(_name, _group);

        return new Result<>();
    }

}