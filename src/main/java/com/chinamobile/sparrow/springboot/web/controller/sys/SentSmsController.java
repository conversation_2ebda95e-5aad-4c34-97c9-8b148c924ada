package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.SentSms;
import com.chinamobile.sparrow.domain.repository.sys.sms.SentSmsRepository;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Controller
@RequestMapping(value = "sys/sms/outbox")
public class SentSmsController {

    protected final SentSmsRepository sentSmsRepository;
    protected final LoginUtil loginUtil;

    public SentSmsController(SentSmsRepository sentSmsRepository, LoginUtil loginUtil) {
        this.sentSmsRepository = sentSmsRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/search")
    @ResponseBody
    @RequiresPermissions(value = "sys:sms:outbox:search")
    public Result<PaginatedRecords<SentSms>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        List<Sorter> _sorters = (List<Sorter>) Optional.ofNullable(data.get("sortBy"))
                .map(i -> i.isJsonNull() ? null : ConverterUtil.json2Object(i.toString(), new TypeToken<List<Sorter>>() {
                }.getType())).orElse(null);
        String _mp = Optional.ofNullable(data.get("mp"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _param = Optional.ofNullable(data.get("param"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        SentSms.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : SentSms.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);
        Date _beginTime = Optional.ofNullable(data.get("beginTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);
        Date _endTime = Optional.ofNullable(data.get("endTime"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd HH:mm:ss")).orElse(null);

        Result<PaginatedRecords<SentSms>> _page = new Result<>();
        _page.data = sentSmsRepository.search(_count, _index, _sorters, _mp, _param, _status, _beginTime, _endTime);
        sentSmsRepository.parseUsers(_page.data.records);
        return _page;
    }

    @PostMapping(value = "/add")
    @ResponseBody
    @RequiresPermissions(value = "sys:sms:outbox:send")
    public Result<String> add(@RequestBody JsonObject data) {
        List<String> _mps = ConverterUtil.json2Object(data.get("mps").toString(), new TypeToken<List<String>>() {
        }.getType());
        String _content = data.get("content").getAsString();
        String _sign = data.get("sign").getAsString();

        return sentSmsRepository.inbox(_mps, _content, _sign, loginUtil.getUserId());
    }

    @PostMapping(value = "/add-by-template")
    @ResponseBody
    @RequiresPermissions(value = "sys:sms:outbox:add")
    public Result<String> addByTemplate(@RequestBody JsonObject data) {
        List<String> _mps = ConverterUtil.json2Object(data.get("mps").toString(), new TypeToken<List<String>>() {
        }.getType());
        String _templateId = data.get("templateId").getAsString();
        List<String> _params = ConverterUtil.json2Object(data.get("params").toString(), new TypeToken<List<String>>() {
        }.getType());
        String _sign = data.get("sign").getAsString();

        return sentSmsRepository.inbox(_mps, _templateId, _params, _sign, loginUtil.getUserId());
    }

    @PostMapping(value = "/cancel")
    @ResponseBody
    @RequiresPermissions(value = "sys:sms:outbox:add")
    public Result<Void> cancel(@RequestBody JsonObject data) {
        String _id = data.get("id").getAsString();

        return sentSmsRepository.cancel(_id, loginUtil.getUserId());
    }

}