package com.chinamobile.sparrow.springboot.web.controller.sys;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

@Controller
@RequestMapping(value = "sys/profile")
public class ProfileController {

    protected final UserRepository userRepository;
    protected final LoginUtil loginUtil;

    public ProfileController(UserRepository userRepository, LoginUtil loginUtil) {
        this.userRepository = userRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "")
    @ResponseBody
    public Result<User> get() {
        return userRepository.getByIdOrAccountOrMp(loginUtil.getUserId(), true);
    }

    @PostMapping(value = "/save")
    @ResponseBody
    public Result<String> save(@RequestBody @Validated User user) throws InstantiationException, IllegalAccessException {
        User _user = loginUtil.getUser();
        user.setId(_user.getId());
        user.setAccount(_user.getAccount());

        return userRepository.save(user, loginUtil.getUserId());
    }

}